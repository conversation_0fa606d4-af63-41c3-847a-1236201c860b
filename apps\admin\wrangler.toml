name = "crebost-admin"
compatibility_date = "2024-01-15"
pages_build_output_dir = ".next"

[env.production]
name = "crebost-admin"
route = { pattern = "admin.crebost.com/*", zone_name = "crebost.com" }

[env.production.vars]
NODE_ENV = "production"
NEXT_PUBLIC_AUTH_URL = "https://auth.crebost.com"
NEXT_PUBLIC_LANDING_URL = "https://landing.crebost.com"
NEXT_PUBLIC_DASHBOARD_URL = "https://dashboard.crebost.com"

[[env.production.d1_databases]]
binding = "DB"
database_name = "crebost-production"
database_id = "23bed93f-2555c-4394-95d8-3408fd23e3e5"

[[env.production.kv_namespaces]]
binding = "SESSIONS"
id = "4ba509d0217e4fa3878c77b9df162ae79"

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "11ac1ff7bee34d709cb2dd15600a17150"

[[env.production.kv_namespaces]]
binding = "ANALYTICS"
id = "647612dcd178469bbf1b2809e4cb3451"

[[env.production.r2_buckets]]
binding = "UPLOADS"
bucket_name = "crebost-uploads"

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.upload]
format = "modules"
