# Crebost Production Environment Variables Template
# Copy this file to .env.production and fill in the actual values

# =============================================================================
# DATABASE CONFIGURATION (Cloudflare D1)
# =============================================================================
DATABASE_URL="file:./dev.db"
# For production, this will be automatically set by Cloudflare D1 binding

# =============================================================================
# BETTER AUTH CONFIGURATION
# =============================================================================
BETTER_AUTH_SECRET="L90cbYFfrXn3Yl1TewISaJLU2bFsSNWN"
BETTER_AUTH_URL="https://auth.crebost.com"

# =============================================================================
# GOOGLE OAUTH CONFIGURATION
# =============================================================================
GOOGLE_CLIENT_ID="your-google-oauth-client-id"
GOOGLE_CLIENT_SECRET="your-google-oauth-client-secret"

# =============================================================================
# MIDTRANS PAYMENT CONFIGURATION
# =============================================================================
MIDTRANS_SERVER_KEY="your-midtrans-server-key"
MIDTRANS_CLIENT_KEY="your-midtrans-client-key"
MIDTRANS_MERCHANT_ID="your-midtrans-merchant-id"
NEXT_PUBLIC_MIDTRANS_CLIENT_KEY="your-midtrans-client-key"

# =============================================================================
# APPLICATION URLS
# =============================================================================
NEXT_PUBLIC_LANDING_URL="https://landing.crebost.com"
NEXT_PUBLIC_AUTH_URL="https://auth.crebost.com"
NEXT_PUBLIC_DASHBOARD_URL="https://dashboard.crebost.com"
NEXT_PUBLIC_ADMIN_URL="https://admin.crebost.com"

# =============================================================================
# CLOUDFLARE CONFIGURATION
# =============================================================================
CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"
CLOUDFLARE_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_ZONE_ID="your-cloudflare-zone-id"

# =============================================================================
# ANALYTICS & TRACKING
# =============================================================================
ANALYTICS_API_KEY="your-internal-analytics-api-key"
GOOGLE_ANALYTICS_ID="your-google-analytics-id"

# =============================================================================
# SOCIAL MEDIA PLATFORM API KEYS (Optional)
# =============================================================================
TIKTOK_API_KEY="your-tiktok-api-key"
TIKTOK_API_SECRET="your-tiktok-api-secret"
INSTAGRAM_API_KEY="your-instagram-api-key"
INSTAGRAM_API_SECRET="your-instagram-api-secret"
YOUTUBE_API_KEY="your-youtube-api-key"
TWITTER_API_KEY="your-twitter-api-key"
TWITTER_API_SECRET="your-twitter-api-secret"

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-smtp-username"
SMTP_PASS="your-smtp-password"
FROM_EMAIL="<EMAIL>"

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE="10485760" # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,video/mp4,video/webm"

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW="900" # 15 minutes in seconds

# =============================================================================
# SECURITY
# =============================================================================
ENCRYPTION_KEY="your-32-character-encryption-key"
JWT_SECRET="your-jwt-secret-key"
WEBHOOK_SECRET="your-webhook-secret-key"

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
LOG_LEVEL="info"
SENTRY_DSN="your-sentry-dsn"
DATADOG_API_KEY="your-datadog-api-key"

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_ANALYTICS="true"
ENABLE_PAYMENTS="true"
ENABLE_EMAIL_NOTIFICATIONS="true"
ENABLE_PUSH_NOTIFICATIONS="false"
MAINTENANCE_MODE="false"

# =============================================================================
# BUSINESS CONFIGURATION
# =============================================================================
PLATFORM_FEE_PERCENTAGE="10"
MINIMUM_CAMPAIGN_BUDGET="1000000" # 1M IDR
MINIMUM_WITHDRAWAL_AMOUNT="50000" # 50K IDR
MAXIMUM_WITHDRAWAL_AMOUNT="50000000" # 50M IDR

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
REDIS_URL="redis://localhost:6379"
CACHE_TTL="3600" # 1 hour in seconds

# =============================================================================
# DEVELOPMENT/STAGING OVERRIDES
# =============================================================================
# Uncomment for staging environment
# NODE_ENV="staging"
# NEXT_PUBLIC_LANDING_URL="https://staging-landing.crebost.com"
# NEXT_PUBLIC_AUTH_URL="https://staging-auth.crebost.com"
# NEXT_PUBLIC_DASHBOARD_URL="https://staging-dashboard.crebost.com"
# NEXT_PUBLIC_ADMIN_URL="https://staging-admin.crebost.com"
