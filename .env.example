# Database
DATABASE_URL="postgresql://username:password@localhost:5432/crebost"
DIRECT_URL="postgresql://username:password@localhost:5432/crebost"

# Supabase (Alternative to self-hosted PostgreSQL)
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Midtrans Payment Gateway
MIDTRANS_SERVER_KEY="your-midtrans-server-key"
MIDTRANS_CLIENT_KEY="your-midtrans-client-key"
MIDTRANS_IS_PRODUCTION="false"

# Cloudflare R2 Storage
CLOUDFLARE_R2_ACCOUNT_ID="your-account-id"
CLOUDFLARE_R2_ACCESS_KEY_ID="your-access-key"
CLOUDFLARE_R2_SECRET_ACCESS_KEY="your-secret-key"
CLOUDFLARE_R2_BUCKET_NAME="crebost-storage"

# Social Media API Keys (for tracking)
TIKTOK_API_KEY="your-tiktok-api-key"
INSTAGRAM_API_KEY="your-instagram-api-key"

# Email Service (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Application Settings
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_LANDING_URL="http://localhost:3000"
NEXT_PUBLIC_AUTH_URL="http://localhost:3001"
NEXT_PUBLIC_DASHBOARD_URL="http://localhost:3002"
NEXT_PUBLIC_ADMIN_URL="http://localhost:3003"

# Business Settings
DEFAULT_CAMPAIGN_BUDGET_USD="1000"
RATE_PER_VIEWER_USD="0.1"
PLATFORM_FEE_PERCENTAGE="10"
MINIMUM_PAYOUT_IDR="50000"
USD_TO_IDR_RATE="15000"
