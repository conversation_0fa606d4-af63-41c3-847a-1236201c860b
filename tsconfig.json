{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@crebost/shared": ["./packages/shared/src"], "@crebost/shared/*": ["./packages/shared/src/*"], "@crebost/database": ["./packages/database/src"], "@crebost/database/*": ["./packages/database/src/*"], "@crebost/ui": ["./packages/ui/src"], "@crebost/ui/*": ["./packages/ui/src/*"], "@crebost/config": ["./packages/config/src"], "@crebost/config/*": ["./packages/config/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", ".next"]}