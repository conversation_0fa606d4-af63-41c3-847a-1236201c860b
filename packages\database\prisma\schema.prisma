// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// SQLite doesn't support enums, so we'll use string fields with constraints

model User {
  id              String    @id @default(cuid())
  email           String    @unique
  name            String
  password        String?   // For credentials login
  avatarUrl       String?   @map("avatar_url")
  role            String    @default("PROMOTER") // CREATOR, PROMOTER, ADMIN
  status          String    @default("ACTIVE") // ACTIVE, SUSPENDED, BANNED
  balanceIdr      Float     @default(0) @map("balance_idr")
  totalEarnedIdr  Float     @default(0) @map("total_earned_idr")
  phone           String?
  bio             String?
  socialLinks     String?   @map("social_links") // JSON as string
  emailVerified   DateTime? @map("email_verified")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  campaigns           Campaign[]
  promotions          Promotion[]
  transactions        Transaction[]
  withdrawalRequests  WithdrawalRequest[]
  reportsSubmitted    Report[] @relation("ReporterReports")
  reportsReceived     Report[] @relation("ReportedUserReports")
  reportsResolved     Report[] @relation("ResolverReports")
  analyticsEvents     AnalyticsEvent[]
  processedWithdrawals WithdrawalRequest[] @relation("ProcessedByAdmin")

  // NextAuth relations
  accounts            Account[]
  sessions            Session[]

  @@map("users")
}

model Campaign {
  id                String  @id @default(cuid())
  creatorId         String  @map("creator_id")
  title             String
  description       String
  budgetUsd         Float   @map("budget_usd")
  budgetIdr         Float   @map("budget_idr")
  ratePerViewerUsd  Float   @map("rate_per_viewer_usd")
  ratePerViewerIdr  Float   @map("rate_per_viewer_idr")
  targetViewers     Int     @map("target_viewers")
  currentViewers    Int     @default(0) @map("current_viewers")
  requirements      String  // JSON as string
  materials         String  // JSON as string
  status            String  @default("DRAFT") // DRAFT, ACTIVE, PAUSED, COMPLETED, CANCELLED
  startDate         DateTime?      @map("start_date")
  endDate           DateTime?      @map("end_date")
  createdAt         DateTime       @default(now()) @map("created_at")
  updatedAt         DateTime       @updatedAt @map("updated_at")

  // Relations
  creator           User           @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  promotions        Promotion[]
  analytics         CampaignAnalytics[]
  reports           Report[]

  @@map("campaigns")
}

model Promotion {
  id              String  @id @default(cuid())
  campaignId      String  @map("campaign_id")
  promoterId      String  @map("promoter_id")
  platform        String  // TIKTOK, INSTAGRAM, YOUTUBE, TWITTER, FACEBOOK
  contentUrl      String  @map("content_url")
  proofUrl        String? @map("proof_url")
  viewersCount    Int     @default(0) @map("viewers_count")
  engagementData  String? @map("engagement_data") // JSON as string
  earningsIdr     Float   @default(0) @map("earnings_idr")
  status          String  @default("PENDING") // PENDING, APPROVED, REJECTED, COMPLETED
  submittedAt     DateTime          @default(now()) @map("submitted_at")
  approvedAt      DateTime?         @map("approved_at")
  rejectedAt      DateTime?         @map("rejected_at")
  rejectionReason String?           @map("rejection_reason")
  createdAt       DateTime          @default(now()) @map("created_at")
  updatedAt       DateTime          @updatedAt @map("updated_at")

  // Relations
  campaign        Campaign          @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  promoter        User              @relation(fields: [promoterId], references: [id], onDelete: Cascade)
  reports         Report[]

  @@map("promotions")
}

model Transaction {
  id            String  @id @default(cuid())
  userId        String  @map("user_id")
  type          String  // DEPOSIT, WITHDRAWAL, EARNING, PAYMENT, REFUND, FEE
  amountIdr     Float   @map("amount_idr")
  description   String
  referenceId   String? @map("reference_id")
  referenceType String? @map("reference_type") // CAMPAIGN, PROMOTION, WITHDRAWAL_REQUEST
  paymentMethod String? @map("payment_method")
  paymentData   String? @map("payment_data") // JSON as string
  status        String  @default("PENDING") // PENDING, COMPLETED, FAILED, CANCELLED
  processedAt   DateTime? @map("processed_at")
  createdAt     DateTime @default(now()) @map("created_at")

  // Relations
  user          User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transactions")
}

model WithdrawalRequest {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  amountIdr     Float     @map("amount_idr")
  bankName      String    @map("bank_name")
  accountNumber String    @map("account_number")
  accountName   String    @map("account_name")
  adminFeeIdr   Float     @map("admin_fee_idr")
  netAmountIdr  Float     @map("net_amount_idr")
  status        String    @default("PENDING") // PENDING, APPROVED, REJECTED, COMPLETED
  processedBy   String?   @map("processed_by")
  processedAt   DateTime? @map("processed_at")
  notes         String?
  createdAt     DateTime  @default(now()) @map("created_at")

  // Relations
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  processor     User?     @relation("ProcessedByAdmin", fields: [processedBy], references: [id])

  @@map("withdrawal_requests")
}

model Report {
  id                  String    @id @default(cuid())
  reporterId          String    @map("reporter_id")
  reportedUserId      String    @map("reported_user_id")
  reportedContentId   String?   @map("reported_content_id")
  reportedContentType String?   @map("reported_content_type") // CAMPAIGN, PROMOTION, USER_PROFILE
  reason              String    // SPAM, FRAUD, INAPPROPRIATE_CONTENT, FAKE_METRICS, OTHER
  description         String
  evidenceUrls        String?   @map("evidence_urls") // JSON as string
  status              String    @default("PENDING") // PENDING, INVESTIGATING, RESOLVED, DISMISSED
  resolvedBy          String?   @map("resolved_by")
  resolvedAt          DateTime? @map("resolved_at")
  resolutionNotes     String?   @map("resolution_notes")
  createdAt           DateTime  @default(now()) @map("created_at")

  // Relations
  reporter            User      @relation("ReporterReports", fields: [reporterId], references: [id], onDelete: Cascade)
  reportedUser        User      @relation("ReportedUserReports", fields: [reportedUserId], references: [id], onDelete: Cascade)
  resolver            User?     @relation("ResolverReports", fields: [resolvedBy], references: [id])
  campaign            Campaign? @relation(fields: [reportedContentId], references: [id])
  promotion           Promotion? @relation(fields: [reportedContentId], references: [id])

  @@map("reports")
}

model AnalyticsEvent {
  id        String   @id @default(cuid())
  userId    String?  @map("user_id")
  eventType String   @map("event_type")
  eventData String   @map("event_data") // JSON as string
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user      User?    @relation(fields: [userId], references: [id])

  @@map("analytics_events")
}

model CampaignAnalytics {
  id                String   @id @default(cuid())
  campaignId        String   @map("campaign_id")
  date              DateTime
  totalViews        Int      @default(0) @map("total_views")
  totalEngagement   Int      @default(0) @map("total_engagement")
  totalSpentIdr     Float    @default(0) @map("total_spent_idr")
  platformBreakdown String?  @map("platform_breakdown") // JSON as string
  createdAt         DateTime @default(now()) @map("created_at")

  // Relations
  campaign          Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)

  @@unique([campaignId, date])
  @@map("campaign_analytics")
}

// NextAuth.js models
model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}
