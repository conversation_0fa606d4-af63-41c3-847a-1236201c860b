{"name": "@crebost/database", "version": "1.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.7.0", "@supabase/supabase-js": "^2.38.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "prisma": "^5.7.0"}}