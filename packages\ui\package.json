{"name": "@crebost/ui", "version": "1.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "class-variance-authority": "^0.7.0", "react-hook-form": "^7.48.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-radio-group": "^1.1.3", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "tailwindcss": "^3.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}