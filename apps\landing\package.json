{"name": "@crebost/landing", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@crebost/shared": "*", "@crebost/ui": "*", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.0.0", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "typescript": "^5.0.0"}}